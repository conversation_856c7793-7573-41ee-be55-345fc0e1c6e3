import pytest
from unittest.mock import patch, MagicMock
from models.interview import (
    EvaluationResult,
    QuestionEvaluation,
    Seniority,
    TranscriptQuestions,
    TranscriptQuestion
)
from controllers.interview_controller import _validate_evaluation_result


class TestInterviewEvaluationEdgeCases:
    """Test cases for interview evaluation edge cases, particularly poor responses like 'i dont know'."""

    def test_validate_evaluation_result_with_poor_responses(self):
        """Test validation function correctly identifies poor responses as answered questions."""
        # Create mock evaluation result with poor responses
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I don't know' showing no experience with <PERSON>act"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate said 'Never used microservices' indicating limited experience"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.MID,
                    explanation="Candidate provided detailed explanation of SQL optimization techniques"
                )
            ],
            percentage_of_match=100.0,  # All 3 questions were answered
            explanation="Overall junior level due to limited experience in most areas"
        )
        
        # Validate the result
        issues = _validate_evaluation_result(result, 3, "test")
        
        # Should have no issues since all questions were properly classified as answered
        assert len(issues) == 0

    def test_validate_evaluation_result_detects_misclassification(self):
        """Test validation function detects when poor responses are incorrectly marked as unanswered."""
        # Create mock evaluation result with misclassified poor responses
        result = EvaluationResult(
            overall_seniority=Seniority.JUNIOR,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="Candidate responded 'I don't know' but this was marked as unanswered"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript"
                )
            ],
            percentage_of_match=50.0,  # Only 1 out of 2 questions marked as answered
            explanation="Limited responses from candidate"
        )
        
        # Validate the result
        issues = _validate_evaluation_result(result, 2, "test")
        
        # Should detect the misclassification issue
        assert len(issues) > 0
        assert any("misclassification" in issue.lower() for issue in issues)

    def test_poor_response_examples(self):
        """Test various poor response examples that should be classified as answered."""
        poor_responses = [
            "i dont know",
            "I don't know",
            "not sure",
            "no experience",
            "never used it",
            "don't have experience",
            "unfamiliar with that",
            "basic understanding only",
            "limited experience"
        ]
        
        for response in poor_responses:
            # Create evaluation with this poor response
            result = EvaluationResult(
                overall_seniority=Seniority.JUNIOR,
                per_question=[
                    QuestionEvaluation(
                        question_number=1,
                        expected_seniority=Seniority.SENIOR,
                        detected_seniority=Seniority.JUNIOR,
                        explanation=f"Candidate responded '{response}' showing limited knowledge"
                    )
                ],
                percentage_of_match=100.0,  # Should be 100% since question was answered
                explanation="Junior level due to limited experience"
            )
            
            # Validate - should have no issues
            issues = _validate_evaluation_result(result, 1, "test")
            assert len(issues) == 0, f"Poor response '{response}' should be classified as answered"

    def test_transcript_with_poor_responses(self):
        """Test transcript containing various poor responses."""
        sample_transcript = """
        INTERVIEWER: Can you describe your experience with .NET Core?
        CANDIDATE: I don't know much about .NET Core.
        
        INTERVIEWER: How do you approach designing microservices?
        CANDIDATE: I'm not sure, never worked with microservices.
        
        INTERVIEWER: What's your experience with React?
        CANDIDATE: No experience with React.
        
        INTERVIEWER: How do you ensure effective communication?
        CANDIDATE: I try to be clear and listen to others.
        
        INTERVIEWER: Can you describe your SQL Server experience?
        CANDIDATE: i dont know
        """
        
        # This transcript should result in:
        # - 5 questions asked
        # - 5 questions answered (even poor responses count as answered)
        # - percentage_of_match should be 100%
        # - Most responses should be rated as 'junior' due to poor quality
        
        expected_questions = TranscriptQuestions(questions=[
            TranscriptQuestion(question_number=1, question_text="Can you describe your experience with .NET Core?"),
            TranscriptQuestion(question_number=2, question_text="How do you approach designing microservices?"),
            TranscriptQuestion(question_number=3, question_text="What's your experience with React?"),
            TranscriptQuestion(question_number=4, question_text="How do you ensure effective communication?"),
            TranscriptQuestion(question_number=5, question_text="Can you describe your SQL Server experience?")
        ])
        
        # The key insight: ALL of these should be classified as answered questions
        # Even "i dont know" is a verbal response and should count toward percentage_of_match
        assert len(expected_questions.questions) == 5

    def test_truly_unanswered_questions(self):
        """Test cases where questions were genuinely not answered."""
        sample_transcript = """
        INTERVIEWER: Can you describe your experience with .NET Core?
        CANDIDATE: I have 3 years of experience with .NET Core development.
        
        INTERVIEWER: How do you approach designing microservices?
        [No response - candidate remained silent]
        
        INTERVIEWER: Let's move on. What's your experience with React?
        CANDIDATE: I've built several React applications.
        """
        
        # This should result in:
        # - 3 questions asked
        # - 2 questions answered (middle question had no response)
        # - percentage_of_match should be 66.7% (2/3 * 100)
        
        expected_result = EvaluationResult(
            overall_seniority=Seniority.MID,
            per_question=[
                QuestionEvaluation(
                    question_number=1,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.MID,
                    explanation="Candidate provided good experience details"
                ),
                QuestionEvaluation(
                    question_number=2,
                    expected_seniority=Seniority.SENIOR,
                    detected_seniority=Seniority.JUNIOR,
                    explanation="No response found in transcript - candidate remained silent"
                ),
                QuestionEvaluation(
                    question_number=3,
                    expected_seniority=Seniority.MID,
                    detected_seniority=Seniority.MID,
                    explanation="Candidate showed practical React experience"
                )
            ],
            percentage_of_match=66.7,  # 2 out of 3 questions answered
            explanation="Good technical knowledge but missed one question"
        )
        
        # Validate this result should pass
        issues = _validate_evaluation_result(expected_result, 3, "test")
        assert len(issues) == 0

    def test_percentage_calculation_accuracy(self):
        """Test that percentage calculations are accurate for various scenarios."""
        test_cases = [
            # (answered_questions, total_questions, expected_percentage)
            (10, 10, 100.0),  # All questions answered
            (8, 10, 80.0),    # 8 out of 10 answered
            (5, 10, 50.0),    # Half answered
            (1, 10, 10.0),    # Only 1 answered
            (0, 10, 0.0),     # None answered
        ]
        
        for answered, total, expected_pct in test_cases:
            # Create mock result
            per_question = []
            for i in range(total):
                if i < answered:
                    # Answered question
                    per_question.append(QuestionEvaluation(
                        question_number=i+1,
                        expected_seniority=Seniority.MID,
                        detected_seniority=Seniority.JUNIOR,
                        explanation="Candidate provided some response"
                    ))
                else:
                    # Unanswered question
                    per_question.append(QuestionEvaluation(
                        question_number=i+1,
                        expected_seniority=Seniority.MID,
                        detected_seniority=Seniority.JUNIOR,
                        explanation="No response found in transcript"
                    ))
            
            result = EvaluationResult(
                overall_seniority=Seniority.JUNIOR,
                per_question=per_question,
                percentage_of_match=expected_pct,
                explanation=f"Test case: {answered}/{total} questions answered"
            )
            
            # Validate - should have no issues for correct percentages
            issues = _validate_evaluation_result(result, total, "test")
            percentage_issues = [issue for issue in issues if "percentage" in issue.lower()]
            assert len(percentage_issues) == 0, f"Percentage calculation should be correct for {answered}/{total}"
